# InstaSaver - Onion Architecture

A FastAPI application for downloading media from Instagram and YouTube, built using Onion Architecture principles.

## Architecture Overview

This application follows the Onion Architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Controllers   │  │     Models      │  │Dependencies │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Use Cases     │  │      DTOs       │  │   Services  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                      Domain Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │    Entities     │  │ Value Objects   │  │ Interfaces  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Infrastructure Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │  Repositories   │  │    Services     │  │   Config    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Key Features

- **Instagram Media Download**: Download photos, videos, and albums from Instagram
- **YouTube Video/Audio Download**: Download videos and audio from YouTube using FastSaver API
- **Music Search**: Search for music using FastSaver API with caching
- **Telegram Integration**: Send downloaded media directly to Telegram chats
- **Error Handling**: Comprehensive error handling with admin notifications
- **Retry Policy**: Automatic retry mechanism with exponential backoff (3 attempts)
- **Caching**: Smart caching for music search results and downloaded media file_ids
- **File Management**: Automatic cleanup of temporary files
- **Format Selection**: Support for multiple video formats (144p to 2160p, mp3)
- **Large File Support**: Handle files up to 2GB using Telegram local API
- **Onion Architecture**: Clean, maintainable code structure

## Layer Descriptions

### Domain Layer (Core)
- **Entities**: Core business objects (`MediaItem`, `DownloadResult`)
- **Value Objects**: Immutable objects (`Url`, `ChatId`, `BotToken`)
- **Interfaces**: Contracts for repositories and services

### Application Layer
- **Use Cases**: Business logic orchestration (`DownloadMediaUseCase`)
- **DTOs**: Data transfer objects for application boundaries
- **Application Services**: Coordinate domain objects and infrastructure

### Infrastructure Layer
- **Repositories**: Data persistence implementations
- **External Services**: Third-party API integrations (Instagram, YouTube, TikTok, Pinterest, Telegram)
- **Configuration**: Dependency injection container

### Presentation Layer
- **Controllers**: HTTP request handlers
- **Models**: Request/response models for API
- **Dependencies**: Dependency injection setup

## Project Structure

```
src/
├── domain/
│   ├── entities/
│   │   ├── media_item.py
│   │   └── download_result.py
│   ├── value_objects/
│   │   ├── url.py
│   │   └── identifiers.py
│   └── interfaces/
│       ├── repositories.py
│       └── services.py
├── application/
│   ├── use_cases/
│   │   └── download_media_use_case.py
│   └── dtos/
│       └── download_dtos.py
├── infrastructure/
│   ├── repositories/
│   │   ├── sqlite_history_repository.py
│   │   └── file_media_repository.py
│   ├── services/
│   │   ├── instagram_service.py
│   │   ├── youtube_service.py
│   │   ├── telegram_service.py
│   │   └── file_service.py
│   └── config/
│       ├── container.py
│       └── settings.py
└── presentation/
    ├── controllers/
    │   └── media_controller.py
    ├── models/
    │   ├── request_models.py
    │   └── response_models.py
    └── dependencies.py
```

**Configuration Files:**
- `env.example`: Environment variables template
- `.env`: Local environment configuration (not in git)
- `app.py`: Main application entry point
- `start_server.py`: Server startup script

**Scripts:**
- `scripts/setup.py`: Interactive setup script
- `scripts/validate_config.py`: Configuration validation script

## Key Benefits

1. **Separation of Concerns**: Each layer has a single responsibility
2. **Dependency Inversion**: Core business logic doesn't depend on external frameworks
3. **Testability**: Easy to unit test each layer in isolation
4. **Maintainability**: Changes in one layer don't affect others
5. **Flexibility**: Easy to swap implementations (e.g., different databases)

## API Endpoints

### POST /api/download
Download media from Instagram or YouTube and send to Telegram.

**Request Body:**
```json
{
  "chat_id": 123456789,
  "url": "https://instagram.com/p/ABC123/",
  "bot_token": "your_bot_token",
  "media_type": "auto"
}
```

**Parameters:**
- `chat_id` (required): Telegram chat ID where media will be sent
- `url` (required): Instagram or YouTube URL to download from
- `bot_token` (required): Telegram bot token
- `media_type` (optional): Type of media to download
  - `"video"`: Download as video (default for YouTube)
  - `"audio"`: Download as audio (YouTube only)
  - `"auto"`: Let system decide (default)

**Response:**
```json
{
  "status": "success",
  "message": "Media sent successfully",
  "file_id": "telegram_file_id"
}
```

**Features:**
- **Bot Username Attribution**: All sent media includes a caption with the bot username and "orqali yuklandi" text
- **Multiple Media Types**: Support for videos, photos, and audio files
- **Large File Support**: Handles files up to 2GB using local Telegram API
- **Album Support**: Downloads Instagram albums with multiple items

**Example Caption:**
```
Original caption text

👉 @YourBotUsername orqali yuklandi
```

### GET /api/health
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2025-06-25T10:30:00"
}
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Install external tools (optional):
```bash
# For large file uploads (optional)
# Install Telegram Bot API server locally
# Follow instructions at: https://github.com/tdlib/telegram-bot-api
```

3. Quick setup (recommended):
```bash
# Run the interactive setup script
python scripts/setup.py
```

Or manual configuration:
```bash
# Copy the example configuration
cp env.example .env

# Edit .env file with your settings
nano .env

# Validate your configuration
python scripts/validate_config.py
```

4. Run the application:
```bash
python app.py
```

The API will be available at `http://localhost:3333`

## Configuration

The application uses the `environs` library for environment variable management, providing type safety and validation. Copy `env.example` to `.env` and update the values:

```bash
cp env.example .env
```

### Environment Variables

The application uses the `environs` library which provides:
- **Type Safety**: Automatic type conversion (str, int, bool)
- **Validation**: Built-in validation for environment variables
- **Default Values**: Clean default value handling
- **Error Handling**: Better error messages for missing or invalid variables

**Server Configuration:**
- `SERVER_HOST`: Server host (default: 0.0.0.0)
- `SERVER_PORT`: Server port (default: 3333)
- `DEBUG`: Enable debug mode (default: false)
- `RELOAD`: Enable auto-reload (default: false)

**Database Configuration:**
- `DATABASE_PATH`: SQLite database file path (default: sqlite3.db)
- `DATABASE_URL`: Database URL (PostgreSQL: postgresql://user:pass@host:port/db)
- `USE_POSTGRES`: Use PostgreSQL instead of SQLite (default: false)

**File Storage Configuration:**
- `DOWNLOADS_DIR`: Directory for temporary downloads (default: downloads)
- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 52428800 - 50MB)

**Telegram Configuration:**
- `TELEGRAM_LOCAL_API_URL`: Local Telegram API URL (default: http://localhost:8081/bot{0}/{1})
- `TELEGRAM_CONNECTION_TIMEOUT`: Connection timeout in seconds (default: 300)
- `TELEGRAM_READ_TIMEOUT`: Read timeout in seconds (default: 300)

**Instagram API Configuration:**
- `INSTAGRAM_API_BASE_URL`: Instagram API base URL (default: https://fastsaverapi.com/get-info)
- `INSTAGRAM_API_TOKEN`: Your Instagram API token (required)

**Music Search API Configuration:**
- `MUSIC_SEARCH_API_BASE_URL`: Music search API base URL (default: https://fastsaverapi.com/search-music)
- `MUSIC_SEARCH_API_TOKEN`: Music search API token (default: lxcMy0OtNaimyGEQkdHjXAmC)

**YouTube Configuration:**
- `YOUTUBE_FASTSAVER_API_TOKEN`: FastSaver API token for YouTube downloads (default: lxcMy0OtNaimyGEQkdHjXAmC)
- `YOUTUBE_FASTSAVER_DOWNLOAD_URL`: FastSaver download API URL (default: https://fastsaverapi.com/download)
- `YOUTUBE_DEFAULT_VIDEO_FORMAT`: Default video format (default: 720p)

**Admin Configuration:**
- `ADMIN_CHAT_ID`: Admin chat ID for error notifications (optional)
- `ADMIN_ENABLE_ERROR_NOTIFICATIONS`: Enable error notifications to admin (default: true)

### Example .env file:

```env
# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=3333
DEBUG=false
RELOAD=false

# Database Configuration
DATABASE_PATH=sqlite3.db
DATABASE_URL=postgresql://saverbot:password@127.0.0.1:5432/saverbot
USE_POSTGRES=true

# File Storage Configuration
DOWNLOADS_DIR=downloads
MAX_FILE_SIZE=52428800

# Telegram Configuration
TELEGRAM_LOCAL_API_URL=http://localhost:8081/bot{0}/{1}
TELEGRAM_CONNECTION_TIMEOUT=300
TELEGRAM_READ_TIMEOUT=300

# Instagram API Configuration
INSTAGRAM_API_BASE_URL=https://fastsaverapi.com/get-info
INSTAGRAM_API_TOKEN=your_instagram_api_token_here

# Music Search API Configuration
MUSIC_SEARCH_API_BASE_URL=https://fastsaverapi.com/search-music
MUSIC_SEARCH_API_TOKEN=lxcMy0OtNaimyGEQkdHjXAmC

# YouTube Configuration
YOUTUBE_FASTSAVER_API_TOKEN=lxcMy0OtNaimyGEQkdHjXAmC
YOUTUBE_FASTSAVER_DOWNLOAD_URL=https://fastsaverapi.com/download
YOUTUBE_DEFAULT_VIDEO_FORMAT=720p

# Admin Configuration
ADMIN_CHAT_ID=your_admin_chat_id_here
ADMIN_ENABLE_ERROR_NOTIFICATIONS=true
```

**Note:** Bot tokens are provided in API requests, not stored in environment variables for security reasons.

## Database Migration

To migrate from SQLite to PostgreSQL, use the provided migration script:

```bash
# Install PostgreSQL dependencies
pip install asyncpg psycopg2-binary

# Run migration
python migrate.py --sqlite-path sqlite3.db --postgres-url postgresql://saverbot:password@127.0.0.1:5432/saverbot

# Or use default values
python migrate.py
```

The migration script will:
- Create PostgreSQL tables automatically (history, music_cache, media_cache)
- Transfer all data from SQLite to PostgreSQL
- Handle timestamp conversions properly
- Skip duplicate entries safely

After successful migration, update your `.env` file to use PostgreSQL:
```env
USE_POSTGRES=true
DATABASE_URL=postgresql://saverbot:password@127.0.0.1:5432/saverbot
```

**Note:** When using `DATABASE_URL`, individual PostgreSQL connection parameters are not needed.

## Dependencies

- **FastAPI**: Web framework
- **python-telegram-bot**: Telegram Bot API
- **pyTelegramBotAPI**: Alternative Telegram library for large files
- **httpx**: HTTP client for external APIs
- **FastSaver API**: YouTube and music download service
- **SQLite**: Database for history storage
- **environs**: Environment variable management with type safety

## Testing

Run tests with:
```bash
pytest tests/
```

## Legacy Code

The original monolithic code has been moved to the `legacy/` directory for reference.

## Contributing

1. Follow the Onion Architecture principles
2. Add new features by extending the appropriate layer
3. Write unit tests for each layer
4. Update documentation for API changes
