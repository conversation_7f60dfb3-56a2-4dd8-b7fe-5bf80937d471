#!/usr/bin/env python3
"""
Migration script to transfer data from SQLite to PostgreSQL.
Bu skript SQLite ma'lumotlarini PostgreSQL ga ko'chiradi.

Usage:
    python migrate.py --sqlite-path sqlite3.db --postgres-url postgresql://saverbot:password@127.0.0.1:5432/saverbot
"""
import asyncio
import sqlite3
import asyncpg
import argparse
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DatabaseMigrator:
    """SQLite dan PostgreSQL ga ma'lumotlarni ko'chirish uchun klass."""
    
    def __init__(self, sqlite_path: str, postgres_url: str):
        self.sqlite_path = sqlite_path
        self.postgres_url = postgres_url
        
    async def migrate(self):
        """Asosiy migration jarayoni."""
        logger.info("Migration boshlandi...")
        
        try:
            # PostgreSQL ga ulanish
            pg_conn = await asyncpg.connect(self.postgres_url)
            logger.info("PostgreSQL ga muvaffaqiyatli ulandi")
            
            # SQLite ga ulanish
            sqlite_conn = sqlite3.connect(self.sqlite_path)
            sqlite_conn.row_factory = sqlite3.Row  # Dict-like access
            logger.info(f"SQLite ga muvaffaqiyatli ulandi: {self.sqlite_path}")
            
            # PostgreSQL jadvallarini yaratish
            await self._create_postgres_tables(pg_conn)
            
            # Ma'lumotlarni ko'chirish
            await self._migrate_history_table(sqlite_conn, pg_conn)
            await self._migrate_music_cache_table(sqlite_conn, pg_conn)
            await self._migrate_media_cache_table(sqlite_conn, pg_conn)
            
            # Ulanishlarni yopish
            sqlite_conn.close()
            await pg_conn.close()
            
            logger.info("Migration muvaffaqiyatli yakunlandi!")
            
        except Exception as e:
            logger.error(f"Migration xatoligi: {e}")
            raise
    
    async def _create_postgres_tables(self, pg_conn: asyncpg.Connection):
        """PostgreSQL jadvallarini yaratish."""
        logger.info("PostgreSQL jadvallarini yaratish...")
        
        # History jadvali
        await pg_conn.execute('''
            CREATE TABLE IF NOT EXISTS history (
                id SERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                url TEXT NOT NULL,
                file_id TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # History uchun indexlar
        await pg_conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_history_user_id ON history(user_id)
        ''')
        await pg_conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_history_timestamp ON history(timestamp)
        ''')
        
        # Music cache jadvali
        await pg_conn.execute('''
            CREATE TABLE IF NOT EXISTS music_cache (
                id SERIAL PRIMARY KEY,
                shortcode TEXT UNIQUE NOT NULL,
                title TEXT NOT NULL,
                duration TEXT NOT NULL,
                thumb TEXT NOT NULL,
                thumb_best TEXT NOT NULL,
                cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Music cache uchun indexlar
        await pg_conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_music_cache_shortcode ON music_cache(shortcode)
        ''')
        await pg_conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_music_cache_cached_at ON music_cache(cached_at)
        ''')

        # Media cache jadvali
        await pg_conn.execute('''
            CREATE TABLE IF NOT EXISTS media_cache (
                id SERIAL PRIMARY KEY,
                shortcode TEXT NOT NULL,
                bot_id TEXT NOT NULL,
                file_id TEXT NOT NULL,
                media_type TEXT NOT NULL CHECK (media_type IN ('audio', 'video')),
                title TEXT,
                duration INTEGER,
                cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(shortcode, bot_id, media_type)
            )
        ''')

        # Media cache uchun indexlar
        await pg_conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_media_cache_lookup
            ON media_cache(shortcode, bot_id, media_type)
        ''')
        await pg_conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_media_cache_cached_at
            ON media_cache(cached_at)
        ''')
        await pg_conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_media_cache_bot_id
            ON media_cache(bot_id)
        ''')

        logger.info("PostgreSQL jadvallari yaratildi")
    
    async def _migrate_history_table(self, sqlite_conn: sqlite3.Connection, pg_conn: asyncpg.Connection):
        """History jadvalini ko'chirish."""
        logger.info("History jadvalini ko'chirish...")
        
        try:
            # SQLite dan ma'lumotlarni o'qish
            cursor = sqlite_conn.cursor()
            cursor.execute("SELECT user_id, url, file_id, timestamp FROM history")
            rows = cursor.fetchall()
            
            if not rows:
                logger.info("History jadvalida ma'lumot topilmadi")
                return
            
            # PostgreSQL ga yozish
            migrated_count = 0
            for row in rows:
                try:
                    # Timestamp ni to'g'ri formatga o'tkazish
                    timestamp_str = row['timestamp']
                    if timestamp_str:
                        try:
                            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        except:
                            timestamp = datetime.now()
                    else:
                        timestamp = datetime.now()
                    
                    await pg_conn.execute(
                        """INSERT INTO history (user_id, url, file_id, timestamp) 
                           VALUES ($1, $2, $3, $4)
                           ON CONFLICT DO NOTHING""",
                        row['user_id'], row['url'], row['file_id'], timestamp
                    )
                    migrated_count += 1
                    
                except Exception as e:
                    logger.warning(f"History qatori ko'chirilmadi: {e}")
                    continue
            
            logger.info(f"History jadvali: {migrated_count} ta qator ko'chirildi")
            
        except sqlite3.OperationalError as e:
            if "no such table: history" in str(e):
                logger.info("SQLite da history jadvali mavjud emas")
            else:
                raise
    
    async def _migrate_music_cache_table(self, sqlite_conn: sqlite3.Connection, pg_conn: asyncpg.Connection):
        """Music cache jadvalini ko'chirish."""
        logger.info("Music cache jadvalini ko'chirish...")
        
        try:
            # SQLite dan ma'lumotlarni o'qish
            cursor = sqlite_conn.cursor()
            cursor.execute("SELECT shortcode, title, duration, thumb, thumb_best, cached_at FROM music_cache")
            rows = cursor.fetchall()
            
            if not rows:
                logger.info("Music cache jadvalida ma'lumot topilmadi")
                return
            
            # PostgreSQL ga yozish
            migrated_count = 0
            for row in rows:
                try:
                    # Timestamp ni to'g'ri formatga o'tkazish
                    cached_at_str = row['cached_at']
                    if cached_at_str:
                        try:
                            cached_at = datetime.fromisoformat(cached_at_str.replace('Z', '+00:00'))
                        except:
                            cached_at = datetime.now()
                    else:
                        cached_at = datetime.now()
                    
                    await pg_conn.execute(
                        """INSERT INTO music_cache (shortcode, title, duration, thumb, thumb_best, cached_at) 
                           VALUES ($1, $2, $3, $4, $5, $6)
                           ON CONFLICT (shortcode) 
                           DO UPDATE SET 
                               title = EXCLUDED.title,
                               duration = EXCLUDED.duration,
                               thumb = EXCLUDED.thumb,
                               thumb_best = EXCLUDED.thumb_best,
                               cached_at = EXCLUDED.cached_at""",
                        row['shortcode'], row['title'], row['duration'], 
                        row['thumb'], row['thumb_best'], cached_at
                    )
                    migrated_count += 1
                    
                except Exception as e:
                    logger.warning(f"Music cache qatori ko'chirilmadi: {e}")
                    continue
            
            logger.info(f"Music cache jadvali: {migrated_count} ta qator ko'chirildi")
            
        except sqlite3.OperationalError as e:
            if "no such table: music_cache" in str(e):
                logger.info("SQLite da music_cache jadvali mavjud emas")
            else:
                raise

    async def _migrate_media_cache_table(self, sqlite_conn: sqlite3.Connection, pg_conn: asyncpg.Connection):
        """Media cache jadvalini ko'chirish."""
        logger.info("Media cache jadvalini ko'chirish...")

        try:
            # SQLite dan ma'lumotlarni o'qish
            cursor = sqlite_conn.cursor()
            cursor.execute("SELECT shortcode, bot_id, file_id, media_type, title, duration, cached_at FROM media_cache")
            rows = cursor.fetchall()

            if not rows:
                logger.info("Media cache jadvalida ma'lumot topilmadi")
                return

            # PostgreSQL ga yozish
            migrated_count = 0
            for row in rows:
                try:
                    # Timestamp ni to'g'ri formatga o'tkazish
                    cached_at_str = row['cached_at']
                    if cached_at_str:
                        try:
                            cached_at = datetime.fromisoformat(cached_at_str.replace('Z', '+00:00'))
                        except:
                            cached_at = datetime.now()
                    else:
                        cached_at = datetime.now()

                    await pg_conn.execute(
                        """INSERT INTO media_cache (shortcode, bot_id, file_id, media_type, title, duration, cached_at)
                           VALUES ($1, $2, $3, $4, $5, $6, $7)
                           ON CONFLICT (shortcode, bot_id, media_type)
                           DO UPDATE SET
                               file_id = EXCLUDED.file_id,
                               title = EXCLUDED.title,
                               duration = EXCLUDED.duration,
                               cached_at = EXCLUDED.cached_at""",
                        row['shortcode'], row['bot_id'], row['file_id'],
                        row['media_type'], row['title'], row['duration'], cached_at
                    )
                    migrated_count += 1

                except Exception as e:
                    logger.warning(f"Media cache qatori ko'chirilmadi: {e}")
                    continue

            logger.info(f"Media cache jadvali: {migrated_count} ta qator ko'chirildi")

        except sqlite3.OperationalError as e:
            if "no such table: media_cache" in str(e):
                logger.info("SQLite da media_cache jadvali mavjud emas")
            else:
                raise


async def main():
    """Asosiy funksiya."""
    parser = argparse.ArgumentParser(description='SQLite dan PostgreSQL ga migration')
    parser.add_argument('--sqlite-path', default='sqlite3.db', 
                       help='SQLite fayl yo\'li (default: sqlite3.db)')
    parser.add_argument('--postgres-url', 
                       default='postgresql://saverbot:password@127.0.0.1:5432/saverbot',
                       help='PostgreSQL ulanish URL si')
    parser.add_argument('--dry-run', action='store_true',
                       help='Faqat tekshirish, haqiqiy migration qilmaslik')
    
    args = parser.parse_args()
    
    if args.dry_run:
        logger.info("DRY RUN rejimi - haqiqiy migration qilinmaydi")
        # Bu yerda faqat ulanishlarni tekshirish mumkin
        return
    
    migrator = DatabaseMigrator(args.sqlite_path, args.postgres_url)
    await migrator.migrate()


if __name__ == "__main__":
    asyncio.run(main())
