"""Use case for downloading from YouTube shortcode."""
import logging

from src.application.dtos.search_dtos import (
    DownloadFromShortcodeRequest,
    DownloadFromShortcodeResponse
)
from src.domain.value_objects.identifiers import Cha<PERSON><PERSON><PERSON>, Bot<PERSON>oken, UserId
from src.domain.interfaces.services import (
    IMusicSearchService,
    ITelegramService,
    IFileService
)
from src.domain.interfaces.repositories import IHistoryRepository
from src.domain.interfaces.media_cache_repository import IMediaCacheRepository
from src.domain.entities.media_cache import MediaCache

logger = logging.getLogger(__name__)


class DownloadFromShortcodeUseCase:
    """Use case for downloading media from YouTube shortcode."""

    def __init__(
        self,
        music_search_service: IMusicSearchService,
        telegram_service: ITelegramService,
        file_service: IFileService,
        history_repository: IHistoryRepository,
        media_cache_repository: IMediaCacheRepository
    ):
        self._music_search_service = music_search_service
        self._telegram_service = telegram_service
        self._file_service = file_service
        self._history_repository = history_repository
        self._media_cache_repository = media_cache_repository

    async def execute(self, request: DownloadFromShortcodeRequest) -> DownloadFromShortcodeResponse:
        """Execute the download from shortcode use case."""
        try:
            logger.info(f"Downloading from shortcode: {request.shortcode}, media_type: {request.media_type}")

            chat_id = ChatId(request.chat_id)
            bot_token = BotToken(request.bot_token)
            user_id = UserId(request.chat_id)  # Using chat_id as user_id for simplicity

            # Get the actual bot username from Telegram API
            try:
                bot_username = await self._telegram_service.get_bot_username_public()
            except Exception as e:
                logger.warning(f"Failed to get bot username: {e}, using fallback")
                bot_username = "instasaver_bot"

            # Create bot_id from bot_username for cache key
            bot_id = bot_username

            # Check cache first
            cached_media = await self._media_cache_repository.get_cached_media(
                shortcode=request.shortcode,
                bot_id=bot_id,
                media_type=request.media_type
            )

            if cached_media:
                logger.info(f"Found cached media for {request.shortcode}:{bot_id}:{request.media_type}")
                try:
                    # Send cached media directly
                    file_id = None
                    if request.media_type == "audio":
                        file_id = await self._telegram_service.send_audio_by_file_id(
                            chat_id=chat_id,
                            file_id=cached_media.file_id,
                            caption=cached_media.title,
                            title=cached_media.title,
                            duration=cached_media.duration
                        )
                    else:  # video
                        file_id = await self._telegram_service.send_video_by_file_id(
                            chat_id=chat_id,
                            file_id=cached_media.file_id,
                            caption=cached_media.title
                        )

                    # Save to history
                    from src.domain.value_objects.url import Url
                    shortcode_identifier = Url(f"youtube://shortcode/{request.shortcode}")
                    await self._history_repository.save_download_history(
                        user_id, shortcode_identifier, file_id
                    )

                    return DownloadFromShortcodeResponse(
                        success=True,
                        message="Media yuborildi (cache dan)",
                        file_id=file_id
                    )

                except Exception as e:
                    logger.warning(f"Failed to send cached media: {e}, downloading fresh")
                    # If cached media fails, continue to download fresh

            # Download media using the music search service (now uses FastSaver API)
            download_result = await self._music_search_service.download_from_shortcode(
                shortcode=request.shortcode,
                media_type=request.media_type,
                bot_username=bot_username
            )

            if not download_result.success:
                # Log detailed error for admin
                logger.error(f"Download failed for shortcode {request.shortcode}: {download_result.message}")

                # Send user-friendly message
                user_message = "Media yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
                await self._telegram_service.send_message(chat_id, user_message)

                # Try to notify admin with detailed error information
                try:
                    from src.presentation.dependencies import get_error_notification_service
                    error_service = get_error_notification_service(request.bot_token)
                    await error_service.notify_admin_error(
                        error_message=f"Download failed for shortcode {request.shortcode}: {download_result.message}",
                        error_type="DOWNLOAD_ERROR",
                        context={
                            "shortcode": request.shortcode,
                            "media_type": request.media_type,
                            "original_error": download_result.message
                        },
                        user_chat_id=str(request.chat_id)
                    )
                except Exception as notification_error:
                    logger.error(f"Failed to send admin notification: {notification_error}")

                return DownloadFromShortcodeResponse(
                    success=False,
                    message=user_message
                )

            # Send media to Telegram using file_id from FastSaver API with retry logic for Topic_closed errors
            file_id = None
            try:
                if request.media_type == "audio":
                    file_id = await self._telegram_service.send_audio_by_file_id(
                        chat_id=chat_id,
                        file_id=download_result.telegram_file_id,
                        caption=download_result.title,  # Use music name as caption
                        title=download_result.title,  # Use music name as title
                        duration=download_result.duration  # Include duration
                    )
                else:  # video
                    file_id = await self._telegram_service.send_video_by_file_id(
                        chat_id=chat_id,
                        file_id=download_result.telegram_file_id,
                        caption=download_result.title  # Use video title as caption
                    )
            except Exception as telegram_error:
                error_str = str(telegram_error).lower()
                if "topic_closed" in error_str or "mavzu yopilgan" in error_str:
                    logger.warning(f"Topic closed error, retrying without thread context: {telegram_error}")
                    try:
                        # Retry without any thread context
                        if request.media_type == "audio":
                            file_id = await self._telegram_service.send_audio_by_file_id(
                                chat_id=chat_id,
                                file_id=download_result.telegram_file_id,
                                caption=download_result.title,
                                title=download_result.title,
                                duration=download_result.duration
                            )
                        else:  # video
                            file_id = await self._telegram_service.send_video_by_file_id(
                                chat_id=chat_id,
                                file_id=download_result.telegram_file_id,
                                caption=download_result.title
                            )
                    except Exception as retry_error:
                        logger.error(f"Failed to send media even after retry: {retry_error}")
                        await self._telegram_service.send_message(
                            chat_id,
                            "Media yuborishda xatolik yuz berdi. Iltimos, boshqa mavzuda yoki shaxsiy xabarda urinib ko'ring."
                        )
                        raise retry_error
                else:
                    # Re-raise other errors
                    raise telegram_error

            # Save to cache for future use
            try:
                media_cache = MediaCache(
                    shortcode=request.shortcode,
                    bot_id=bot_id,
                    file_id=download_result.telegram_file_id,  # Use original file_id from FastSaver
                    media_type=request.media_type,
                    title=download_result.title,
                    duration=download_result.duration
                )
                await self._media_cache_repository.save_media_cache(media_cache)
                logger.info(f"Saved media to cache: {media_cache.cache_key}")
            except Exception as cache_error:
                logger.warning(f"Failed to save media to cache: {cache_error}")

            # Save to history (using shortcode directly instead of YouTube URL)
            # Create a simple URL-like identifier for the shortcode
            from src.domain.value_objects.url import Url
            shortcode_identifier = Url(f"youtube://shortcode/{request.shortcode}")
            await self._history_repository.save_download_history(
                user_id, shortcode_identifier, file_id
            )

            # No file cleanup needed since we're using file_id instead of local files

            return DownloadFromShortcodeResponse(
                success=True,
                message=f"Media yuklandi va yuborildi",
                file_id=file_id
            )

        except Exception as e:
            logger.error(f"Error in download from shortcode use case: {e}")

            # Send user-friendly message
            user_message = "Media yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            try:
                await self._telegram_service.send_message(ChatId(request.chat_id), user_message)
            except Exception as msg_error:
                logger.error(f"Failed to send error message: {msg_error}")

            # Try to notify admin with detailed error information
            try:
                from src.presentation.dependencies import get_error_notification_service
                error_service = get_error_notification_service(request.bot_token)
                error_type = error_service.classify_error(e)
                await error_service.notify_admin_error(
                    error_message=str(e),
                    error_type=error_type,
                    context={
                        "shortcode": request.shortcode,
                        "media_type": request.media_type,
                        "use_case": "download_from_shortcode"
                    },
                    user_chat_id=str(request.chat_id)
                )
            except Exception as notification_error:
                logger.error(f"Failed to send admin notification: {notification_error}")

            return DownloadFromShortcodeResponse(
                success=False,
                message=user_message
            )
