"""Media cache entity for storing downloaded media file_ids."""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional


@dataclass
class MediaCache:
    """Domain entity representing cached media file_id."""
    shortcode: str
    bot_id: str  # Bot token hash or bot username
    file_id: str
    media_type: str  # "audio" or "video"
    title: Optional[str] = None
    duration: Optional[int] = None
    cached_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Validate the media cache entry."""
        if not self.shortcode:
            raise ValueError("Shortcode cannot be empty")
        if not self.bot_id:
            raise ValueError("Bot ID cannot be empty")
        if not self.file_id:
            raise ValueError("File ID cannot be empty")
        if not self.media_type:
            raise ValueError("Media type cannot be empty")
        if self.media_type not in ["audio", "video"]:
            raise ValueError("Media type must be 'audio' or 'video'")
        if self.cached_at is None:
            self.cached_at = datetime.now()
    
    @property
    def cache_key(self) -> str:
        """Get unique cache key for this media."""
        return f"{self.shortcode}:{self.bot_id}:{self.media_type}"
