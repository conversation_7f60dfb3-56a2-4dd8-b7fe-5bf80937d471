"""PostgreSQL implementation of history repository."""
import logging
import asyncpg
from datetime import datetime
from typing import Optional, List

from src.domain.interfaces.repositories import IHistoryRepository
from src.domain.value_objects.identifiers import UserId
from src.domain.value_objects.url import Url
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class PostgresHistoryRepository(IHistoryRepository):
    """PostgreSQL implementation of history repository."""

    def __init__(self, connection_pool=None):
        if asyncpg is None:
            raise ImportError("asyncpg is required for PostgreSQL support. Install with: pip install asyncpg")
        self.connection_pool = connection_pool
        self._connection_string = self._build_connection_string()
        self._initialized = False

    def _build_connection_string(self) -> str:
        """Build PostgreSQL connection string."""
        if settings.database.url:
            return settings.database.url

        # Fallback to default if no URL provided
        return "postgresql://saverbot:password@127.0.0.1:5432/saverbot"

    async def _get_connection(self):
        """Get database connection."""
        if self.connection_pool:
            return await self.connection_pool.acquire()
        return await asyncpg.connect(self._connection_string)

    async def _release_connection(self, conn):
        """Release database connection."""
        if self.connection_pool:
            await self.connection_pool.release(conn)
        else:
            await conn.close()

    async def init_db(self):
        """Initialize the database tables."""
        conn = await self._get_connection()
        try:
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS history (
                    id SERIAL PRIMARY KEY,
                    user_id BIGINT NOT NULL,
                    url TEXT NOT NULL,
                    file_id TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create index for better performance
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_history_user_id ON history(user_id)
            ''')
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_history_timestamp ON history(timestamp)
            ''')
            
            logger.info("History table initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing history table: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def _ensure_initialized(self):
        """Ensure database is initialized."""
        if not self._initialized:
            await self.init_db()
            self._initialized = True

    async def save_download_history(
        self,
        user_id: UserId,
        url: Url,
        telegram_file_id: str,
        timestamp: Optional[datetime] = None
    ) -> None:
        """Save download history."""
        await self._ensure_initialized()
        conn = await self._get_connection()
        try:
            timestamp_value = timestamp or datetime.now()

            await conn.execute(
                """INSERT INTO history (user_id, url, file_id, timestamp)
                   VALUES ($1, $2, $3, $4)""",
                user_id.value, url.value, telegram_file_id, timestamp_value
            )

            logger.info(f"Saved download history for user {user_id.value}")

        except Exception as e:
            logger.error(f"Error saving download history: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def get_download_history(
        self,
        user_id: UserId,
        url: Url
    ) -> Optional[str]:
        """Get download history for a specific user and URL."""
        await self._ensure_initialized()
        conn = await self._get_connection()
        try:
            result = await conn.fetchrow(
                """SELECT file_id FROM history 
                   WHERE user_id = $1 AND url = $2 
                   ORDER BY timestamp DESC LIMIT 1""",
                user_id.value, url.value
            )
            
            if result:
                logger.info(f"Found download history for user {user_id.value}")
                return result['file_id']
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting download history: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def get_user_history(
        self, 
        user_id: UserId, 
        limit: int = 10
    ) -> List[dict]:
        """Get user's download history."""
        conn = await self._get_connection()
        try:
            results = await conn.fetch(
                """SELECT url, file_id, timestamp FROM history 
                   WHERE user_id = $1 
                   ORDER BY timestamp DESC LIMIT $2""",
                user_id.value, limit
            )
            
            history = []
            for row in results:
                history.append({
                    'url': row['url'],
                    'file_id': row['file_id'],
                    'timestamp': row['timestamp']
                })
            
            logger.info(f"Retrieved {len(history)} history records for user {user_id.value}")
            return history
            
        except Exception as e:
            logger.error(f"Error getting user history: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def cleanup_old_history(self, days: int = 30) -> int:
        """Clean up old history records."""
        conn = await self._get_connection()
        try:
            result = await conn.execute(
                """DELETE FROM history 
                   WHERE timestamp < NOW() - INTERVAL '%s days'""" % days
            )
            
            # Extract number of deleted rows from result
            deleted_count = int(result.split()[-1]) if result else 0
            
            logger.info(f"Cleaned up {deleted_count} old history records")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up old history: {e}")
            raise
        finally:
            await self._release_connection(conn)
