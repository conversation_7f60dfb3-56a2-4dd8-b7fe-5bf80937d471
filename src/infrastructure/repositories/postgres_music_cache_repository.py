"""PostgreSQL implementation of music cache repository."""
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional

import asyncpg

from src.domain.interfaces.repositories import IMusicCacheRepository
from src.domain.entities.music_search_result import MusicSearchResult
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class PostgresMusicCacheRepository(IMusicCacheRepository):
    """PostgreSQL implementation of music cache repository."""

    def __init__(self, connection_pool=None):
        if asyncpg is None:
            raise ImportError("asyncpg is required for PostgreSQL support. Install with: pip install asyncpg")
        self.connection_pool = connection_pool
        self._connection_string = self._build_connection_string()
        self._initialized = False

    def _build_connection_string(self) -> str:
        """Build PostgreSQL connection string."""
        if settings.database.url:
            return settings.database.url

        # Fallback to default if no URL provided
        return "postgresql://saverbot:password@127.0.0.1:5432/saverbot"

    async def _get_connection(self):
        """Get database connection."""
        if self.connection_pool:
            return await self.connection_pool.acquire()
        return await asyncpg.connect(self._connection_string)

    async def _release_connection(self, conn):
        """Release database connection."""
        if self.connection_pool:
            await self.connection_pool.release(conn)
        else:
            await conn.close()

    async def init_db(self):
        """Initialize the database tables."""
        conn = await self._get_connection()
        try:
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS music_cache (
                    id SERIAL PRIMARY KEY,
                    shortcode TEXT UNIQUE NOT NULL,
                    title TEXT NOT NULL,
                    duration TEXT NOT NULL,
                    thumb TEXT NOT NULL,
                    thumb_best TEXT NOT NULL,
                    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create index for better performance
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_music_cache_shortcode ON music_cache(shortcode)
            ''')
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_music_cache_cached_at ON music_cache(cached_at)
            ''')
            
            logger.info("Music cache table initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing music cache table: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def _ensure_initialized(self):
        """Ensure database is initialized."""
        if not self._initialized:
            await self.init_db()
            self._initialized = True

    async def save_search_result(self, shortcode: str, result: MusicSearchResult) -> None:
        """Save search result to cache."""
        await self._ensure_initialized()
        conn = await self._get_connection()
        try:
            cached_at = datetime.now()
            
            # Use INSERT ... ON CONFLICT to update existing entries
            await conn.execute(
                """INSERT INTO music_cache 
                   (shortcode, title, duration, thumb, thumb_best, cached_at) 
                   VALUES ($1, $2, $3, $4, $5, $6)
                   ON CONFLICT (shortcode) 
                   DO UPDATE SET 
                       title = EXCLUDED.title,
                       duration = EXCLUDED.duration,
                       thumb = EXCLUDED.thumb,
                       thumb_best = EXCLUDED.thumb_best,
                       cached_at = EXCLUDED.cached_at""",
                shortcode, result.title, result.duration, result.thumb, result.thumb_best, cached_at
            )
            
            logger.info(f"Cached search result for shortcode: {shortcode}")
            
        except Exception as e:
            logger.error(f"Error saving search result to cache: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def get_cached_result(self, shortcode: str) -> Optional[MusicSearchResult]:
        """Get cached search result."""
        await self._ensure_initialized()
        conn = await self._get_connection()
        try:
            # Check if cache is still valid (1 day)
            expiry_time = datetime.now() - timedelta(days=1)
            
            result = await conn.fetchrow(
                """SELECT title, duration, thumb, thumb_best FROM music_cache 
                   WHERE shortcode = $1 AND cached_at > $2""",
                shortcode, expiry_time
            )
            
            if result:
                logger.info(f"Found cached result for shortcode: {shortcode}")
                return MusicSearchResult(
                    title=result['title'],
                    shortcode=shortcode,
                    duration=result['duration'],
                    thumb=result['thumb'],
                    thumb_best=result['thumb_best']
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached result: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def cleanup_expired_cache(self, days: int = 7) -> int:
        """Clean up expired cache entries."""
        conn = await self._get_connection()
        try:
            expiry_time = datetime.now() - timedelta(days=days)
            
            result = await conn.execute(
                """DELETE FROM music_cache WHERE cached_at < $1""",
                expiry_time
            )
            
            # Extract number of deleted rows from result
            deleted_count = int(result.split()[-1]) if result else 0
            
            logger.info(f"Cleaned up {deleted_count} expired cache entries")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired cache: {e}")
            raise
        finally:
            await self._release_connection(conn)

    async def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        conn = await self._get_connection()
        try:
            # Get total count
            total_result = await conn.fetchrow("SELECT COUNT(*) as total FROM music_cache")
            total_count = total_result['total'] if total_result else 0
            
            # Get count of entries from last 24 hours
            recent_time = datetime.now() - timedelta(days=1)
            recent_result = await conn.fetchrow(
                "SELECT COUNT(*) as recent FROM music_cache WHERE cached_at > $1",
                recent_time
            )
            recent_count = recent_result['recent'] if recent_result else 0
            
            # Get oldest entry
            oldest_result = await conn.fetchrow(
                "SELECT MIN(cached_at) as oldest FROM music_cache"
            )
            oldest_entry = oldest_result['oldest'] if oldest_result else None
            
            return {
                'total_entries': total_count,
                'recent_entries': recent_count,
                'oldest_entry': oldest_entry.isoformat() if oldest_entry else None
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            raise
        finally:
            await self._release_connection(conn)
