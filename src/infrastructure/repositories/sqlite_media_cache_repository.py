"""SQLite implementation of media cache repository."""
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Optional

from src.domain.interfaces.media_cache_repository import IMediaCacheRepository
from src.domain.entities.media_cache import MediaCache
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class SQLiteMediaCacheRepository(IMediaCacheRepository):
    """SQLite implementation of media cache repository."""

    def __init__(self, db_path: str = None):
        self.db_path = db_path or settings.database.path
        self._init_db()

    def _init_db(self):
        """Initialize the database."""
        conn = sqlite3.connect(self.db_path)
        c = conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS media_cache 
                     (id INTEGER PRIMARY KEY AUTOINCREMENT,
                      shortcode TEXT NOT NULL, 
                      bot_id TEXT NOT NULL,
                      file_id TEXT NOT NULL,
                      media_type TEXT NOT NULL CHECK (media_type IN ('audio', 'video')),
                      title TEXT, 
                      duration INTEGER, 
                      cached_at TEXT NOT NULL,
                      UNIQUE(shortcode, bot_id, media_type))''')
        
        # Create indexes for better performance
        c.execute('''CREATE INDEX IF NOT EXISTS idx_media_cache_lookup 
                     ON media_cache(shortcode, bot_id, media_type)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_media_cache_cached_at 
                     ON media_cache(cached_at)''')
        c.execute('''CREATE INDEX IF NOT EXISTS idx_media_cache_bot_id 
                     ON media_cache(bot_id)''')
        
        conn.commit()
        conn.close()

    def _get_connection(self) -> sqlite3.Connection:
        """Get database connection."""
        return sqlite3.connect(self.db_path)

    async def save_media_cache(self, media_cache: MediaCache) -> None:
        """Save media cache entry."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            cached_at = media_cache.cached_at.isoformat()
            
            # Use INSERT OR REPLACE to update existing entries
            c.execute(
                """INSERT OR REPLACE INTO media_cache 
                   (shortcode, bot_id, file_id, media_type, title, duration, cached_at) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (media_cache.shortcode, media_cache.bot_id, media_cache.file_id, 
                 media_cache.media_type, media_cache.title, media_cache.duration, cached_at)
            )
            conn.commit()
            conn.close()
            
            logger.info(f"Cached media: {media_cache.cache_key}")
            
        except Exception as e:
            logger.error(f"Error saving media cache: {e}")
            raise

    async def get_cached_media(
        self, 
        shortcode: str, 
        bot_id: str, 
        media_type: str
    ) -> Optional[MediaCache]:
        """Get cached media by shortcode, bot_id and media_type."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            # Check if cache is still valid (30 days)
            expiry_time = datetime.now() - timedelta(days=30)
            expiry_str = expiry_time.isoformat()
            
            c.execute(
                """SELECT shortcode, bot_id, file_id, media_type, title, duration, cached_at 
                   FROM media_cache 
                   WHERE shortcode = ? AND bot_id = ? AND media_type = ? AND cached_at > ?""",
                (shortcode, bot_id, media_type, expiry_str)
            )
            
            row = c.fetchone()
            conn.close()
            
            if row:
                shortcode, bot_id, file_id, media_type, title, duration, cached_at = row
                
                logger.info(f"Found cached media: {shortcode}:{bot_id}:{media_type}")
                return MediaCache(
                    shortcode=shortcode,
                    bot_id=bot_id,
                    file_id=file_id,
                    media_type=media_type,
                    title=title,
                    duration=duration,
                    cached_at=datetime.fromisoformat(cached_at)
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached media: {e}")
            raise

    async def cleanup_expired_cache(self, days: int = 30) -> int:
        """Clean up expired cache entries."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            expiry_time = datetime.now() - timedelta(days=days)
            expiry_str = expiry_time.isoformat()
            
            c.execute("DELETE FROM media_cache WHERE cached_at < ?", (expiry_str,))
            deleted_count = c.rowcount
            
            conn.commit()
            conn.close()
            
            logger.info(f"Cleaned up {deleted_count} expired media cache entries")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired media cache: {e}")
            raise

    async def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            # Get total count
            c.execute("SELECT COUNT(*) FROM media_cache")
            total_count = c.fetchone()[0]
            
            # Get count by media type
            c.execute("SELECT COUNT(*) FROM media_cache WHERE media_type = 'audio'")
            audio_count = c.fetchone()[0]
            
            c.execute("SELECT COUNT(*) FROM media_cache WHERE media_type = 'video'")
            video_count = c.fetchone()[0]
            
            # Get count of entries from last 24 hours
            recent_time = datetime.now() - timedelta(days=1)
            recent_str = recent_time.isoformat()
            c.execute("SELECT COUNT(*) FROM media_cache WHERE cached_at > ?", (recent_str,))
            recent_count = c.fetchone()[0]
            
            # Get oldest entry
            c.execute("SELECT MIN(cached_at) FROM media_cache")
            oldest_entry = c.fetchone()[0]
            
            conn.close()
            
            return {
                'total_entries': total_count,
                'audio_entries': audio_count,
                'video_entries': video_count,
                'recent_entries': recent_count,
                'oldest_entry': oldest_entry
            }
            
        except Exception as e:
            logger.error(f"Error getting media cache stats: {e}")
            raise
