"""Telegram service implementation."""
import os
import logging
from typing import Optional
from telegram import Bo<PERSON>
from telegram.error import Telegram<PERSON>rror, BadRequest, Forbidden, NetworkError
import telebot

from src.domain.interfaces.services import ITelegramService
from src.domain.value_objects.identifiers import Chat<PERSON><PERSON>, BotToken
from src.infrastructure.config.settings import settings
from src.infrastructure.utils.text_utils import TextUtils

logger = logging.getLogger(__name__)


class TelegramService(ITelegramService):
    """Telegram service implementation."""

    def __init__(self, bot_token: BotToken):
        self.bot_token = bot_token
        self.bot = Bot(token=bot_token.value)
        self.telebot_instance = telebot.TeleBot(token=bot_token.value)
        self._bot_username = None
        
        # Configure telebot for large files
        self._configure_telebot()

    def _configure_telebot(self):
        """Configure telebot for large file uploads."""
        telebot.apihelper.API_URL = settings.telegram.local_api_url
        telebot.apihelper.READ_TIMEOUT = settings.telegram.read_timeout
        telebot.apihelper.CONNECT_TIMEOUT = settings.telegram.connection_timeout

    def _handle_telegram_error(self, error: Exception, operation: str) -> str:
        """Handle Telegram-specific errors and return user-friendly message."""
        error_str = str(error).lower()
        
        # Handle specific Telegram errors
        if "topic_closed" in error_str:
            logger.warning(f"Topic closed error in {operation}: {error}")
            return "Uzr, bu mavzu yopilgan. Iltimos, boshqa mavzuda yoki shaxsiy xabarda urinib ko'ring."
        
        elif "bot was blocked" in error_str or "bot was stopped" in error_str:
            logger.warning(f"Bot blocked/stopped error in {operation}: {error}")
            return "Uzr, bot bloklangan yoki to'xtatilgan. Iltimos, botni qayta ishga tushiring."
        
        elif "chat not found" in error_str or "chat_id" in error_str:
            logger.warning(f"Chat not found error in {operation}: {error}")
            return "Uzr, chat topilmadi. Iltimos, botni qayta ishga tushiring."
        
        elif "forbidden" in error_str or "not enough rights" in error_str:
            logger.warning(f"Permission error in {operation}: {error}")
            return "Uzr, botda yetarli huquqlar yo'q. Iltimos, botga kerakli huquqlarni bering."
        
        elif "network" in error_str or "timeout" in error_str:
            logger.warning(f"Network error in {operation}: {error}")
            return "Tarmoq xatosi. Iltimos, keyinroq qayta urinib ko'ring."
        
        elif "file too large" in error_str or "file size" in error_str:
            logger.warning(f"File too large error in {operation}: {error}")
            return "Fayl juda katta. Iltimos, kichikroq fayl tanlang."
        
        else:
            logger.error(f"Unexpected Telegram error in {operation}: {error}")
            return "Telegram xatosi yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."

    async def _get_bot_username(self) -> str:
        """Get bot username from Telegram API."""
        if self._bot_username is None:
            try:
                bot_info = await self.bot.get_me()
                self._bot_username = bot_info.username
                logger.info(f"Bot username retrieved: {self._bot_username}")
            except Exception as e:
                logger.error(f"Error getting bot username: {e}")
                self._bot_username = "YuklaydiBot"  # Fallback username
        return self._bot_username

    async def get_bot_username_public(self) -> str:
        """Public method to get bot username."""
        return await self._get_bot_username()

    def _add_bot_username_to_caption(self, caption: Optional[str]) -> str:
        """Add bot username to caption with proper length handling."""
        bot_username = self._bot_username or "YuklaydiBot"

        logger.debug(f"Adding bot username to caption. Input: {repr(caption)}, Bot: {bot_username}")

        # Use safe caption method that guarantees proper length
        formatted_caption = TextUtils.get_safe_caption_for_media(
            title=caption,
            bot_username=bot_username,
            max_length=settings.telegram.max_caption_length
        )

        logger.debug(f"Final caption: {repr(formatted_caption)} (length: {len(formatted_caption)})")

        return formatted_caption

    async def send_photo(
        self, 
        chat_id: ChatId, 
        photo_path: str, 
        caption: Optional[str] = None,
        message_thread_id: Optional[int] = None,
        is_direct_link: Optional[bool] = False
    ) -> str:
        """Send photo to Telegram chat. Returns file_id."""
        try:
            if is_direct_link:
                message = await self.bot.send_photo(
                    chat_id=chat_id.value,
                    photo=photo_path,
                    caption=caption
                )
                return message.photo[-1].file_id

            if not os.path.exists(photo_path):
                raise FileNotFoundError(f"Photo file not found: {photo_path}")

            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            # Log the parameters for debugging
            logger.debug(f"Sending photo to chat_id: {chat_id.value}, thread_id: {message_thread_id}")

            with open(photo_path, "rb") as photo_file:
                # Only include message_thread_id if it's provided and valid
                if message_thread_id is not None and message_thread_id > 0:
                    msg = await self.bot.send_photo(
                        chat_id=chat_id.value,
                        photo=photo_file,
                        caption=enhanced_caption,
                        message_thread_id=message_thread_id
                    )
                else:
                    msg = await self.bot.send_photo(
                        chat_id=chat_id.value,
                        photo=photo_file,
                        caption=enhanced_caption
                    )
                
            if msg and msg.photo:
                file_id = msg.photo[-1].file_id
                logger.info(f"Photo sent successfully, file_id: {file_id}")
                return file_id
            else:
                raise Exception("Failed to send photo - no file_id returned")
                
        except (TelegramError, BadRequest, Forbidden, NetworkError) as e:
            error_message = self._handle_telegram_error(e, "send_photo")
            logger.error(f"Telegram error sending photo: {e}")
            raise Exception(error_message)
        except Exception as e:
            # Check if error is related to caption length
            error_msg = str(e).lower()
            if "caption" in error_msg and ("too long" in error_msg or "length" in error_msg):
                logger.warning(f"Caption too long error, retrying with truncated caption: {e}")
                try:
                    # Retry with just bot mention
                    bot_username = self._bot_username or "YuklaydiBot"
                    fallback_caption = f"👉 @{bot_username} orqali yuklandi"

                    # Force truncate fallback caption if needed
                    if len(fallback_caption) > settings.telegram.max_caption_length:
                        if settings.telegram.max_caption_length > 3:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length-3] + "..."
                        else:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length]

                    with open(photo_path, "rb") as photo_file:
                        # Retry without message_thread_id to avoid topic issues
                        msg = await self.bot.send_photo(
                            chat_id=chat_id.value,
                            photo=photo_file,
                            caption=fallback_caption
                        )

                    if msg and msg.photo:
                        file_id = msg.photo[-1].file_id
                        logger.info(f"Photo sent successfully with fallback caption, file_id: {file_id}")
                        return file_id
                except Exception as retry_e:
                    logger.error(f"Error sending photo with fallback caption: {retry_e}")

            logger.error(f"Error sending photo: {e}")
            raise

    async def send_video(
        self, 
        chat_id: ChatId, 
        video_path: str, 
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        width: Optional[int] = None,
        height: Optional[int] = None,
        thumbnail_path: Optional[str] = None,
        message_thread_id: Optional[int] = None,
        is_direct_link: Optional[bool] = False
    ) -> str:
        """Send video to Telegram chat. Returns file_id."""
        try:
            if is_direct_link:
                message = await self.bot.send_video(
                    chat_id=chat_id.value,
                    video=video_path,
                    caption=caption,
                    duration=duration,
                )
                return message.video.file_id

            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")

            file_size = os.path.getsize(video_path) / (1024 * 1024)  # Size in MB

            if file_size > 2048:  # 2GB in MB
                raise ValueError("Video file size exceeds 2GB limit")

            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            # Log the parameters for debugging
            logger.debug(f"Sending video to chat_id: {chat_id.value}, thread_id: {message_thread_id}, size: {file_size:.2f}MB")

            # For large files, use telebot with local API
            if file_size > 50:  # Use telebot for files larger than 50MB
                return await self._send_large_video(
                    chat_id, video_path, enhanced_caption, duration, width, height, thumbnail_path, message_thread_id
                )
            else:
                # Use python-telegram-bot for smaller files
                with open(video_path, "rb") as video_file:
                    # Only include message_thread_id if it's provided and valid
                    if message_thread_id is not None and message_thread_id > 0:
                        msg = await self.bot.send_video(
                            chat_id=chat_id.value,
                            video=video_file,
                            caption=enhanced_caption,
                            duration=duration,
                            width=width,
                            height=height,
                            message_thread_id=message_thread_id
                        )
                    else:
                        msg = await self.bot.send_video(
                            chat_id=chat_id.value,
                            video=video_file,
                            caption=enhanced_caption,
                            duration=duration,
                            width=width,
                            height=height
                        )
                    
                if msg and msg.video:
                    file_id = msg.video.file_id
                    logger.info(f"Video sent successfully, file_id: {file_id}")
                    return file_id
                else:
                    raise Exception("Failed to send video - no file_id returned")
                    
        except (TelegramError, BadRequest, Forbidden, NetworkError) as e:
            error_message = self._handle_telegram_error(e, "send_video")
            logger.error(f"Telegram error sending video: {e}")
            raise Exception(error_message)
        except Exception as e:
            # Check if error is related to caption length
            error_msg = str(e).lower()
            if "caption" in error_msg and ("too long" in error_msg or "length" in error_msg):
                logger.warning(f"Caption too long error, retrying with truncated caption: {e}")
                try:
                    # Retry with just bot mention
                    bot_username = self._bot_username or "YuklaydiBot"
                    fallback_caption = f"👉 @{bot_username} orqali yuklandi"

                    # Force truncate fallback caption if needed
                    if len(fallback_caption) > settings.telegram.max_caption_length:
                        if settings.telegram.max_caption_length > 3:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length-3] + "..."
                        else:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length]

                    # For large files, use telebot with local API
                    if file_size > 50:  # Use telebot for files larger than 50MB
                        return await self._send_large_video(
                            chat_id, video_path, fallback_caption, duration, width, height, thumbnail_path
                        )
                    else:
                        with open(video_path, "rb") as video_file:
                            # Retry without message_thread_id to avoid topic issues
                            msg = await self.bot.send_video(
                                chat_id=chat_id.value,
                                video=video_file,
                                caption=fallback_caption,
                                duration=duration,
                                width=width,
                                height=height
                            )

                        if msg and msg.video:
                            file_id = msg.video.file_id
                            logger.info(f"Video sent successfully with fallback caption, file_id: {file_id}")
                            return file_id
                except Exception as retry_e:
                    logger.error(f"Error sending video with fallback caption: {retry_e}")

            logger.error(f"Error sending video: {e}")
            raise

    async def send_audio(
        self, 
        chat_id: ChatId, 
        audio_path: str, 
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        title: Optional[str] = None,
        performer: Optional[str] = None,
        message_thread_id: Optional[int] = None,
        is_direct_link: Optional[bool] = False
    ) -> str:
        """Send audio to Telegram chat. Returns file_id."""
        try:
            if is_direct_link:
                message = await self.bot.send_audio(
                    chat_id=chat_id.value,
                    audio=audio_path,
                    caption=caption,
                    duration=duration,
                )
                return message.audio.file_id

            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Audio file not found: {audio_path}")

            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            # Log the parameters for debugging
            logger.debug(f"Sending audio to chat_id: {chat_id.value}, thread_id: {message_thread_id}")

            with open(audio_path, "rb") as audio_file:
                # Only include message_thread_id if it's provided and valid
                if message_thread_id is not None and message_thread_id > 0:
                    msg = await self.bot.send_audio(
                        chat_id=chat_id.value,
                        audio=audio_file,
                        caption=enhanced_caption,
                        duration=duration,
                        title=title,
                        performer=performer,
                        message_thread_id=message_thread_id
                    )
                else:
                    msg = await self.bot.send_audio(
                        chat_id=chat_id.value,
                        audio=audio_file,
                        caption=enhanced_caption,
                        duration=duration,
                        title=title,
                        performer=performer
                    )

            if msg and msg.audio:
                file_id = msg.audio.file_id
                logger.info(f"Audio sent successfully, file_id: {file_id}")
                return file_id
            else:
                raise Exception("Failed to send audio - no file_id returned")

        except (TelegramError, BadRequest, Forbidden, NetworkError) as e:
            error_message = self._handle_telegram_error(e, "send_audio")
            logger.error(f"Telegram error sending audio: {e}")
            raise Exception(error_message)
        except Exception as e:
            # Check if error is related to caption length
            error_msg = str(e).lower()
            if "caption" in error_msg and ("too long" in error_msg or "length" in error_msg):
                logger.warning(f"Caption too long error, retrying with truncated caption: {e}")
                try:
                    # Retry with just bot mention
                    bot_username = self._bot_username or "YuklaydiBot"
                    fallback_caption = f"👉 @{bot_username} orqali yuklandi"

                    # Force truncate fallback caption if needed
                    if len(fallback_caption) > settings.telegram.max_caption_length:
                        if settings.telegram.max_caption_length > 3:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length-3] + "..."
                        else:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length]

                    with open(audio_path, "rb") as audio_file:
                        # Retry without message_thread_id to avoid topic issues
                        msg = await self.bot.send_audio(
                            chat_id=chat_id.value,
                            audio=audio_file,
                            caption=fallback_caption,
                            duration=duration,
                            title=title,
                            performer=performer
                        )

                    if msg and msg.audio:
                        file_id = msg.audio.file_id
                        logger.info(f"Audio sent successfully with fallback caption, file_id: {file_id}")
                        return file_id
                except Exception as retry_e:
                    logger.error(f"Error sending audio with fallback caption: {retry_e}")

            logger.error(f"Error sending audio: {e}")
            raise

    async def _send_large_video(
        self,
        chat_id: ChatId,
        video_path: str,
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        width: Optional[int] = None,
        height: Optional[int] = None,
        thumbnail_path: Optional[str] = None,
        message_thread_id: Optional[int] = None
    ) -> str:
        """Send large video using telebot with local API."""
        try:
            # Log the parameters for debugging
            logger.debug(f"Sending large video to chat_id: {chat_id.value}, thread_id: {message_thread_id}")
            
            with open(video_path, 'rb') as video:
                thumbnail = None
                if thumbnail_path and os.path.exists(thumbnail_path):
                    thumbnail = open(thumbnail_path, 'rb')

                try:
                    # Note: telebot doesn't support message_thread_id directly
                    # We'll use the basic send_video method
                    msg = self.telebot_instance.send_video(
                        chat_id=chat_id.value,
                        video=video,
                        caption=caption,
                        duration=duration,
                        width=width,
                        height=height,
                        thumbnail=thumbnail,
                        supports_streaming=True
                    )
                    
                    if msg and msg.video:
                        file_id = msg.video.file_id
                        logger.info(f"Large video sent successfully, file_id: {file_id}")
                        return file_id
                    else:
                        raise Exception("Failed to send large video - no file_id returned")
                        
                finally:
                    if thumbnail:
                        thumbnail.close()
                        
        except Exception as e:
            logger.error(f"Error sending large video: {e}")
            raise

    async def send_message(self, chat_id: ChatId, text: str, message_thread_id: Optional[int] = None) -> None:
        """Send text message to Telegram chat."""
        try:
            # Log the parameters for debugging
            logger.debug(f"Sending message to chat_id: {chat_id.value}, thread_id: {message_thread_id}")
            
            # Only include message_thread_id if it's provided and valid
            if message_thread_id is not None and message_thread_id > 0:
                await self.bot.send_message(
                    chat_id=chat_id.value, 
                    text=text,
                    message_thread_id=message_thread_id
                )
            else:
                await self.bot.send_message(chat_id=chat_id.value, text=text)
                
            logger.info(f"Message sent to chat {chat_id.value}")

        except (TelegramError, BadRequest, Forbidden, NetworkError) as e:
            error_message = self._handle_telegram_error(e, "send_message")
            logger.error(f"Telegram error sending message: {e}")
            raise Exception(error_message)
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise

    async def send_audio_by_file_id(
        self,
        chat_id: ChatId,
        file_id: str,
        caption: Optional[str] = None,
        title: Optional[str] = None,
        performer: Optional[str] = None,
        duration: Optional[int] = None,
        message_thread_id: Optional[int] = None
    ) -> str:
        """Send audio to Telegram chat using file_id. Returns file_id."""
        try:
            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            # Log the parameters for debugging
            logger.debug(f"Sending audio by file_id to chat_id: {chat_id.value}, thread_id: {message_thread_id}")

            # Only include message_thread_id if it's provided and valid
            if message_thread_id is not None and message_thread_id > 0:
                msg = await self.bot.send_audio(
                    chat_id=chat_id.value,
                    audio=file_id,
                    caption=enhanced_caption,
                    title=title,
                    performer=performer,
                    duration=duration,
                    message_thread_id=message_thread_id
                )
            else:
                msg = await self.bot.send_audio(
                    chat_id=chat_id.value,
                    audio=file_id,
                    caption=enhanced_caption,
                    title=title,
                    performer=performer,
                    duration=duration
                )

            if msg and msg.audio:
                new_file_id = msg.audio.file_id
                logger.info(f"Audio sent by file_id successfully, new file_id: {new_file_id}")
                return new_file_id
            else:
                raise Exception("Failed to send audio by file_id - no file_id returned")

        except (TelegramError, BadRequest, Forbidden, NetworkError) as e:
            error_message = self._handle_telegram_error(e, "send_audio_by_file_id")
            logger.error(f"Telegram error sending audio by file_id: {e}")
            raise Exception(error_message)
        except Exception as e:
            # Check if error is related to caption length
            error_msg = str(e).lower()
            if "caption" in error_msg and ("too long" in error_msg or "length" in error_msg):
                logger.warning(f"Caption too long error, retrying with truncated caption: {e}")
                try:
                    # Retry with just bot mention
                    bot_username = self._bot_username or "YuklaydiBot"
                    fallback_caption = f"👉 @{bot_username} orqali yuklandi"

                    # Force truncate fallback caption if needed
                    if len(fallback_caption) > settings.telegram.max_caption_length:
                        if settings.telegram.max_caption_length > 3:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length-3] + "..."
                        else:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length]

                    # Retry without message_thread_id to avoid topic issues
                    msg = await self.bot.send_audio(
                        chat_id=chat_id.value,
                        audio=file_id,
                        caption=fallback_caption,
                        title=title,
                        performer=performer,
                        duration=duration
                    )

                    if msg and msg.audio:
                        new_file_id = msg.audio.file_id
                        logger.info(f"Audio sent by file_id successfully with fallback caption, new file_id: {new_file_id}")
                        return new_file_id
                except Exception as retry_e:
                    logger.error(f"Error sending audio by file_id with fallback caption: {retry_e}")

            logger.error(f"Error sending audio by file_id: {e}")
            raise

    async def send_video_by_file_id(
        self,
        chat_id: ChatId,
        file_id: str,
        caption: Optional[str] = None,
        message_thread_id: Optional[int] = None
    ) -> str:
        """Send video to Telegram chat using file_id. Returns file_id."""
        try:
            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            # Log the parameters for debugging
            logger.debug(f"Sending video by file_id to chat_id: {chat_id.value}, thread_id: {message_thread_id}")

            # Only include message_thread_id if it's provided and valid
            if message_thread_id is not None and message_thread_id > 0:
                msg = await self.bot.send_video(
                    chat_id=chat_id.value,
                    video=file_id,
                    caption=enhanced_caption,
                    message_thread_id=message_thread_id
                )
            else:
                msg = await self.bot.send_video(
                    chat_id=chat_id.value,
                    video=file_id,
                    caption=enhanced_caption
                )

            if msg and msg.video:
                new_file_id = msg.video.file_id
                logger.info(f"Video sent by file_id successfully, new file_id: {new_file_id}")
                return new_file_id
            else:
                raise Exception("Failed to send video by file_id - no file_id returned")

        except (TelegramError, BadRequest, Forbidden, NetworkError) as e:
            error_message = self._handle_telegram_error(e, "send_video_by_file_id")
            logger.error(f"Telegram error sending video by file_id: {e}")
            raise Exception(error_message)
        except Exception as e:
            # Check if error is related to caption length
            error_msg = str(e).lower()
            if "caption" in error_msg and ("too long" in error_msg or "length" in error_msg):
                logger.warning(f"Caption too long error, retrying with truncated caption: {e}")
                try:
                    # Retry with just bot mention
                    bot_username = self._bot_username or "YuklaydiBot"
                    fallback_caption = f"👉 @{bot_username} orqali yuklandi"

                    # Force truncate fallback caption if needed
                    if len(fallback_caption) > settings.telegram.max_caption_length:
                        if settings.telegram.max_caption_length > 3:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length-3] + "..."
                        else:
                            fallback_caption = fallback_caption[:settings.telegram.max_caption_length]

                    # Retry without message_thread_id to avoid topic issues
                    msg = await self.bot.send_video(
                        chat_id=chat_id.value,
                        video=file_id,
                        caption=fallback_caption
                    )

                    if msg and msg.video:
                        new_file_id = msg.video.file_id
                        logger.info(f"Video sent by file_id successfully with fallback caption, new file_id: {new_file_id}")
                        return new_file_id
                except Exception as retry_e:
                    logger.error(f"Error sending video by file_id with fallback caption: {retry_e}")

            logger.error(f"Error sending video by file_id: {e}")
            raise
